import React from 'react';
import { Box, Grid, useTheme, Paper, Button, Typography } from '@mui/material';
import AddIcon from '@mui/icons-material/Add';

export interface WidgetConfig {
  id: string;
  type: string;
  title: string;
  width: number; // Grid width (1-12)
  height?: number; // Optional fixed height in pixels
  props?: Record<string, any>; // Additional props for the widget
  position?: number; // Position in the grid
}

interface DashboardWidgetGridProps {
  widgets: WidgetConfig[];
  onAddWidget?: () => void;
  onRemoveWidget?: (id: string) => void;
  onMoveWidget?: (id: string, direction: 'up' | 'down') => void;
  onConfigureWidget?: (id: string) => void;
  renderWidget: (config: WidgetConfig, index: number) => React.ReactNode;
  spacing?: number;
  addWidgetButtonText?: string;
  emptyStateMessage?: string;
  showAddButton?: boolean;
}

const DashboardWidgetGrid: React.FC<DashboardWidgetGridProps> = ({
  widgets,
  onAddWidget,
  onRemoveWidget,
  onMoveWidget,
  onConfigureWidget,
  renderWidget,
  spacing = 3,
  addWidgetButtonText = 'Add Widget',
  emptyStateMessage = 'No widgets to display. Add a widget to get started.',
  showAddButton = true
}) => {
  const theme = useTheme();

  // Sort widgets by position if available
  const sortedWidgets = [...widgets].sort((a, b) => {
    if (a.position !== undefined && b.position !== undefined) {
      return a.position - b.position;
    }
    return 0;
  });

  return (
    <Box sx={{ width: '100%' }}>
      <Grid container spacing={spacing}>
        {sortedWidgets.map((widget, index) => (
          <Grid
            item
            key={widget.id}
            xs={12}
            sm={widget.width <= 6 ? 6 : 12}
            md={widget.width}
            sx={{
              height: widget.height ? `${widget.height}px` : 'auto',
              transition: 'all 0.3s ease'
            }}
          >
            {renderWidget(widget, index)}
          </Grid>
        ))}

        {widgets.length === 0 && (
          <Grid item xs={12}>
            <Paper
              sx={{
                p: 4,
                textAlign: 'center',
                borderRadius: 2,
                bgcolor: theme.palette.mode === 'dark'
                  ? 'rgba(255, 255, 255, 0.05)'
                  : 'rgba(0, 0, 0, 0.02)',
                border: '1px dashed',
                borderColor: 'divider'
              }}
            >
              <Typography variant="body1" color="text.secondary" gutterBottom>
                {emptyStateMessage}
              </Typography>

              {showAddButton && onAddWidget && (
                <Button
                  variant="outlined"
                  startIcon={<AddIcon />}
                  onClick={onAddWidget}
                  sx={{ mt: 2 }}
                >
                  {addWidgetButtonText}
                </Button>
              )}
            </Paper>
          </Grid>
        )}

        {widgets.length > 0 && showAddButton && onAddWidget && (
          <Grid item xs={12}>
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                mt: 2
              }}
            >
              <Button
                variant="outlined"
                startIcon={<AddIcon />}
                onClick={onAddWidget}
                sx={{
                  borderStyle: 'dashed'
                }}
              >
                {addWidgetButtonText}
              </Button>
            </Box>
          </Grid>
        )}
      </Grid>
    </Box>
  );
};

export default DashboardWidgetGrid;
